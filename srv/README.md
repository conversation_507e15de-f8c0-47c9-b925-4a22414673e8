# 华夏漫游小程序服务端

本项目是“华夏漫游”微信小程序的服务端，主要为小程序提供 RESTful API 接口，以及为管理员提供管理后台。

## 技术选型

- OS：Ubuntu 22.04 及以上版本
- Language：Python 3.12 及以上版本
- Web Framework：FastAPI
- DB：PostgreSQL 15
- Cache：Redis 7
- HTTP Server：Nginx
- Template：Jinja2
- ORM：Tortoise ORM

## RESTful API

1. 获取首页轮播图及热展列表
    - API: `/api/home`
    - method: GET
    - arguments: None
    - returns: {}

2. 获取数字展览列表
    - API: `/api/ticket/digital`
    - method: GET
    - arguments: None
    - returns: {}

3. 获取空间剧场列表
    - API: `/api/ticket/theater`
    - method: GET
    - arguments: None
    - returns: {}

4. 获取联票列表
    - API: `/api/ticket/joint`
    - method: GET
    - arguments: None
    - returns: {}

5. 获取票价信息
    - API: `/api/ticket/detail`
    - method: GET
    - arguments: None
    - returns: {}

6. 预约
    - API: `/api/`
    - method: GET
    - arguments: None
    - returns: {}

7. 下单
    - API: `/api/`
    - method: GET
    - arguments: None
    - returns: {}

8. 支付
    - API: `/api/`
    - method: GET
    - arguments: None
    - returns: {}

9. 获取订单列表
    - API: `/api/`
    - method: GET
    - arguments: None
    - returns: {}

10. 获取预约列表
    - API: `/api/`
    - method: GET
    - arguments: None
    - returns: {}

11. 获取个人信息
    - API: `/api/`
    - method: GET
    - arguments: None
    - returns: {}
