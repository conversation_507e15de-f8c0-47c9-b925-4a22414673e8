/* pages/reservation/index/index.wxss */

@import '/app.wxss';

.container {
  padding: 200rpx 0 20rpx 0;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 85px;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  padding-bottom: 20rpx;
  box-sizing: border-box;
  z-index: 1000;
  background-color: #ffffff;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.navbar-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #000000;
}

/* 预约列表 */
.reservation-list {
  padding: 20rpx;
}

.reservation-item {
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  display: flex;
  align-items: flex-start;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.reservation-thumbnail {
  width: 160rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.reservation-info {
  flex: 1;
  margin-right: 20rpx;
}

.reservation-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
  line-height: 1.4;
  margin-bottom: 16rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

.reservation-period {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 8rpx;
}

.reservation-location {
  font-size: 24rpx;
  color: #666666;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
}

.reservation-action {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.reservation-btn {
  background-color: #4d65e0;
  color: #ffffff;
  font-size: 24rpx;
  padding: 16rpx 32rpx;
  border-radius: 50rpx;
  border: none;
  font-weight: normal;
  line-height: 1;
}

.reservation-btn::after {
  border: none;
}

.empty-reservations {
  text-align: center;
  color: #999999;
  font-size: 28rpx;
  padding: 100rpx 0;
}
