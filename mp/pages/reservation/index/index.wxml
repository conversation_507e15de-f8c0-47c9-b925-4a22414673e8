<view class="container">
  <!-- 自定义导航栏 -->
  <view class="custom-navbar" style="padding-top: {{statusBarHeight}}px;">
    <view class="navbar-title">观展预约</view>
  </view>

  <!-- 预约列表 -->
  <view class="reservation-list">
    <view class="reservation-item" wx:for="{{reservations}}" wx:key="id">
      <image class="reservation-thumbnail" src="{{item.thumbnail}}" mode="aspectFill"></image>
      <view class="reservation-info">
        <view class="reservation-title">{{item.title}}</view>
        <view class="reservation-period">展期：{{item.period}}</view>
        <view class="reservation-location">地点：{{item.location}}</view>
      </view>
      <view class="reservation-action">
        <button class="reservation-btn" bindtap="onReservationTap" data-id="{{item.id}}">去预约</button>
      </view>
    </view>

    <view wx:if="{{reservations.length === 0}}" class="empty-reservations">
      暂无可预约的展览
    </view>
  </view>
</view>
