/* pages/index/index.wxss */

@import '/app.wxss';

.container {
  padding: 200rpx 0;
}

/* 0. 标题栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 85px;
  display: flex;
  align-items: center;
  padding-left: 30rpx;
  box-sizing: border-box;
  z-index: 1000;
  background: transparent;
}

/* 标题 */
.navbar-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 0 4rpx rgba(10, 9, 9, 0.5);
}

.logo {
  height: 27rpx;
  width: 27rpx;
}

/* 1. 轮播区域 */
.swiper-container {
  width: 100%;
  height: 500rpx;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
}

/* 轮播图片 */
.swiper-image {
  width: 100%;
  height: 100%;
}

/* 调整轮小圆点 */
.swiper-container .wx-swiper-dots {
  bottom: 70rpx;
}

/* 2. 导航区 */
.nav-section {
  display: flex;
  justify-content: space-around;
  position: relative;
  width: 95%;
  background-color: #ffffff;
  padding: 30rpx;
  z-index: 10;
  margin-top: 250rpx;
  border-radius: 20rpx;
  box-sizing: border-box;
  box-shadow: 0 0 20rpx rgba(0, 0, 0, 0.2);
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 24rpx;
  color: #333;
}

.nav-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 10rpx;
}

/* 3. 热展推荐区 */
.hot-exhibitions-section {
  width: 95%;
  margin: 20rpx auto;
  background-color: #ffffff;
  box-sizing: border-box;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx 0;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.section-more {
  font-size: 24rpx;
  color: #999999;
}

.exhibition-list {
  display: flex;
  flex-direction: column;
}

.exhibition-item {
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
}

.exhibition-image {
  width: 100%;
  height: 390rpx;
  display: block;
}

.exhibition-info {
  padding: 20rpx;
}

.exhibition-title {
  display: block;
  font-size: 28rpx;
  color: #333333;
  font-weight: bold;
  margin-bottom: 8rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
