@import '/app.wxss';

.container {
  padding: 350rpx 20rpx 20rpx 20rpx;
}

.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
  background-color: #fff;
  box-shadow: 0 0 3rpx rgba(0, 0, 0, 0.05);
}

/* 自定义导航栏样式 */
.custom-navbar {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  height: 190rpx;
  padding: 30rpx;
  box-sizing: border-box;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.navbar-city {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #333;
}

.navbar-city-icon {
  width: 24rpx;
  height: 24rpx;
  margin-left: 8rpx;
}

.navbar-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #000;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.nav-bar {
  display: flex;
  height: 80rpx;
  align-items: center;
  justify-content: space-around;
}

.nav-item {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  font-size: 30rpx;
  color: #666;
  position: relative;
  text-align: center;
}

.nav-item.active {
  color: #333;
  font-weight: bold;
}

.nav-item.active::after {
  content: '';
  position: absolute;
  bottom: 10rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 6rpx;
  background-color: #007aff;
  border-radius: 3rpx;
}

.sub-nav-bar {
  display: flex;
  padding: 15rpx;
  border-bottom: 1rpx solid #eee;
  white-space: nowrap;
  /* 确保子导航项在一行显示 */
}

.sub-nav-item {
  display: inline-block;
  padding: 5rpx 20rpx;
  font-size: 22rpx;
  color: #333;
  /* background-color: #f5f5f5; */
  border-radius: 30rpx;
  margin-right: 20rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.1);
}

.sub-nav-item.active {
  color: #fff;
  background-color: #4d65e0;
  border-color: #4d65e0;
}

/* 票券列表样式 */
.ticket-list {
  padding: 20rpx 0;
}

.ticket-item {
  display: flex;
  background-color: #fff;
  padding: 20rpx;
  margin-bottom: 20rpx;
  border-radius: 16rpx;
  box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.1);
}

.ticket-thumbnail {
  width: 180rpx;
  height: 240rpx;
  border-radius: 10rpx;
  margin-right: 20rpx;
}

.ticket-info {
  display: flex;
  flex-direction: column;
  flex: 1;
  position: relative;
  width: 100%;
}

.ticket-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 10rpx;
  padding-right: 120rpx;
  width: 100%;
}

.ticket-addr {
  font-size: 24rpx;
  color: #888;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-right: 120rpx;
}

.ticket-price {
  position: absolute;
  right: 0;
  top: 40%;
  display: flex;
  font-size: 24rpx;
  color: #4d4ccd;
  white-space: nowrap;
  height: 100%;
  align-items: baseline;
}

.ticket-price .price-symbol {
  font-size: 28rpx;
  margin-right: 4rpx;
}

.ticket-price .price-value {
  font-size: 50rpx;
  font-weight: bold;
  margin-right: 4rpx;
}

.ticket-price .price-suffix {
  font-size: 24rpx;
  color: #888;
}

.empty-tickets,
.empty-search-result {
  text-align: center;
  color: #999;
  font-size: 28rpx;
  padding: 40rpx 0;
}

/* 城市选择浮层样式 (基本不变) */
.city-picker-popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #fff;
  z-index: 1000;
  transform: translateX(100%);
  transition: transform 0.3s ease-in-out;
  display: flex;
  flex-direction: column;
}

.city-picker-popup.active {
  transform: translateX(0);
}

.popup-header {
  display: flex;
  align-items: flex-end;
  justify-content: center;
  padding: 30rpx;
  height: 190rpx;
  border-bottom: 1rpx solid #f0f0f0;
  box-sizing: border-box;
  background-color: #fff;
}

.popup-header .header-placeholder {
  width: 36rpx;
}

.popup-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #000;
  text-align: center;
  flex-grow: 1;
}

.search-bar-container {
  padding: 20rpx 30rpx;
  background-color: #fff;
  position: relative;
}

.search-input {
  height: 72rpx;
  line-height: 72rpx;
  background-color: #f7f7f7;
  border-radius: 36rpx;
  padding-left: 70rpx;
  padding-right: 30rpx;
  font-size: 28rpx;
  color: #333;
  width: 100%;
  box-sizing: border-box;
}

.search-input::placeholder {
  color: #aaa;
}

.search-bar-container .icon-search {
  position: absolute;
  left: 55rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 32rpx;
  color: #aaa;
}

.current-selection-section,
.all-cities-section {
  padding: 20rpx 30rpx;
}

.section-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: bold;
}

.city-tag {
  display: inline-block;
  background-color: #f0f0f0;
  color: #333;
  padding: 12rpx 30rpx;
  border-radius: 30rpx;
  font-size: 28rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
}

.city-tag.active {
  background-color: #4d65e0;
  color: #fff;
}

.city-list-scroll {
  flex-grow: 1;
  height: 0;
  background-color: #fff;
  position: relative;
}

.city-letter-header {
  font-size: 28rpx;
  color: #333;
  padding: 20rpx 0;
  background-color: #fff;
  font-weight: bold;
}

.city-name {
  font-size: 30rpx;
  color: #333;
  padding: 25rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.city-name:last-child {
  border-bottom: none;
}

.alphabet-indexer {
  position: fixed;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 10rpx;
  z-index: 1001;
  background-color: transparent;
}

.alphabet-letter {
  font-size: 22rpx;
  color: #4d65e0;
  padding: 6rpx 8rpx;
  text-align: center;
}