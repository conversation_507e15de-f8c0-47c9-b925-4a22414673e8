Page({
  data: {
    selectedCity: '全国', // 初始选中的城市
    showCityPickerPopup: false, // 控制城市选择浮层显示
    searchText: '', // 搜索框内容
    allCities: [
      // 示例城市数据
      '北京',
      '保定',
      '成都',
      '重庆',
      '长沙',
      '广州',
      '桂林',
      '合肥',
      '哈尔滨',
      '济南',
      '嘉兴',
      '昆明',
      '南京',
      '南昌',
      '南宁',
      '上海',
      '深圳',
      '苏州',
      '沈阳',
      '天津',
      '武汉',
      '厦门',
      '西安',
      '郑州',
      '中山',
      '珠海'
    ],
    groupedCities: [], // 按字母分组的城市列表
    alphabet: [], // 字母索引
    scrollToLetter: '', // 用于scroll-view的scroll-into-view
    originalGroupedCities: [], // 保存原始的分组城市数据，用于搜索后恢复

    // 恢复的展览列表相关数据
    activeNav: 'all',
    tickets: [
      {
        id: 1,
        thumbnail: 'https://disk.seamile.cn/mp/e.jpg',
        title: '【贵阳】《罗纪》',
        addr: '贵阳·壹号购物中心',
        price: '68'
      },
      {
        id: 2,
        thumbnail: 'https://disk.seamile.cn/mp/f.jpg',
        title: '【滁州】宇宙猜想·启程&丛林探秘·熊猫...',
        addr: '滁州·吾悦广场',
        price: '68'
      },
      {
        id: 3,
        thumbnail: 'https://disk.seamile.cn/mp/g.jpg',
        title: '【青岛】宇宙猜想·启程&丛林探秘·重返...',
        addr: '青岛·金茂览秀城',
        price: '88'
      },
      {
        id: 4,
        thumbnail: 'https://disk.seamile.cn/mp/h.jpg',
        title: '【宁波】丛林探秘·重返侏罗纪&丛林探...',
        addr: '宁波鄞州区·宁波博物馆',
        price: '68'
      },
      {
        id: 5,
        thumbnail: 'https://disk.seamile.cn/mp/e.jpg',
        title: '【贵阳】《丛林探秘I重返侏罗纪》',
        addr: '贵阳·壹号购物中心',
        price: '68'
      },
      {
        id: 6,
        thumbnail: 'https://disk.seamile.cn/mp/f.jpg',
        title: '【滁州】宇宙猜想·启程&丛林探秘·熊猫...',
        addr: '滁州·吾悦广场',
        price: '68'
      },
      {
        id: 7,
        thumbnail: 'https://disk.seamile.cn/mp/g.jpg',
        title: '【青岛】宇宙猜想·启程&丛林探秘·重返...',
        addr: '青岛·金茂览秀城',
        price: '88'
      },
      {
        id: 8,
        thumbnail: 'https://disk.seamile.cn/mp/h.jpg',
        title: '【宁波】丛林探秘·重返侏罗纪&丛林探...',
        addr: '宁波鄞州区·宁波博物馆',
        price: '68'
      }
    ]
  },

  onLoad: function (options) {
    this.processCities();
    // 如果有加载票务的逻辑，也应在此处调用
    // this.loadTickets();
  },

  // 处理城市数据，进行分组和生成字母表
  processCities: function () {
    const getFirstLetter = (str) => {
      if (!str) return '#';
      const char = str.charAt(0);
      const pinyinMap = {
        // 极简拼音映射，仅供演示
        北京: 'B',
        保定: 'B',
        成都: 'C',
        滁州: 'C',
        常州: 'C',
        重庆: 'C',
        长沙: 'C',
        东莞: 'D',
        大连: 'D',
        佛山: 'F',
        福州: 'F',
        广州: 'G',
        贵阳: 'G',
        桂林: 'G',
        杭州: 'H',
        合肥: 'H',
        哈尔滨: 'H',
        济南: 'J',
        嘉兴: 'J',
        昆明: 'K',
        廊坊: 'L',
        临沂: 'L',
        南京: 'N',
        宁波: 'N',
        南昌: 'N',
        南宁: 'N',
        青岛: 'Q',
        泉州: 'Q',
        上海: 'S',
        深圳: 'S',
        苏州: 'S',
        沈阳: 'S',
        石家庄: 'S',
        天津: 'T',
        太原: 'T',
        唐山: 'T',
        武汉: 'W',
        无锡: 'W',
        温州: 'W',
        厦门: 'X',
        西安: 'X',
        徐州: 'X',
        烟台: 'Y',
        扬州: 'Y',
        郑州: 'Z',
        中山: 'Z',
        珠海: 'Z',
        淄博: 'Z'
      };
      if (pinyinMap[str]) return pinyinMap[str];
      if (/[a-zA-Z]/.test(char)) return char.toUpperCase();
      return '#';
    };

    const cities = this.data.allCities.sort((a, b) => {
      const letterA = getFirstLetter(a);
      const letterB = getFirstLetter(b);
      if (letterA < letterB) return -1;
      if (letterA > letterB) return 1;
      return a.localeCompare(b, 'zh-CN');
    });

    const grouped = {};
    cities.forEach((city) => {
      const letter = getFirstLetter(city);
      if (!grouped[letter]) {
        grouped[letter] = [];
      }
      grouped[letter].push(city);
    });

    const groupedCities = Object.keys(grouped)
      .sort()
      .map((letter) => ({
        letter: letter,
        cities: grouped[letter]
      }));
    const alphabet = groupedCities.map((group) => group.letter);
    this.setData({
      groupedCities: groupedCities,
      originalGroupedCities: JSON.parse(JSON.stringify(groupedCities)),
      alphabet: alphabet
    });
  },

  openCityPicker: function () {
    this.setData({ showCityPickerPopup: true });
  },

  closeCityPicker: function () {
    this.setData({ showCityPickerPopup: false, searchText: '' });
    this.setData({ groupedCities: this.data.originalGroupedCities });
  },

  onSearchInput: function (e) {
    const searchText = e.detail.value.trim();
    this.setData({ searchText: searchText });
    if (!searchText) {
      this.setData({ groupedCities: this.data.originalGroupedCities });
      return;
    }
    const filteredGroupedCities = this.data.originalGroupedCities
      .map((group) => {
        const filteredCities = group.cities.filter((city) => city.includes(searchText));
        return { ...group, cities: filteredCities };
      })
      .filter((group) => group.cities.length > 0);
    this.setData({ groupedCities: filteredGroupedCities });
  },

  selectCurrentCity: function (e) {
    const city = e.currentTarget.dataset.city;
    const finalCity = city === '全部城市' ? '全国' : city;
    this.setData({
      selectedCity: finalCity,
      showCityPickerPopup: false,
      searchText: ''
    });
    this.setData({ groupedCities: this.data.originalGroupedCities });
    console.log('当前选择城市:', finalCity, '，应根据此城市刷新展览列表');
    // this.loadTickets({ city: finalCity }); // 示例：刷新票务
  },

  onCityTap: function (e) {
    const city = e.currentTarget.dataset.city;
    this.setData({
      selectedCity: city,
      showCityPickerPopup: false,
      searchText: ''
    });
    this.setData({ groupedCities: this.data.originalGroupedCities });
    console.log('选择了城市:', city, '，应根据此城市刷新展览列表');
    // this.loadTickets({ city: city }); // 示例：刷新票务
  },

  scrollToLetter: function (e) {
    const letter = e.currentTarget.dataset.letter;
    this.setData({ scrollToLetter: 'letter-' + letter });
  },

  // 恢复的导航栏点击事件
  onNavTap: function (e) {
    const navType = e.currentTarget.dataset.type;
    if (this.data.activeNav === navType) {
      return;
    }
    this.setData({
      activeNav: navType
    });
    console.log('切换导航:', navType, '，应根据此分类和当前城市刷新展览列表');
    // this.loadTickets({ category: navType, city: this.data.selectedCity }); // 示例：刷新票务
  }

  // loadTickets: function(params = {}) {
  //   console.log("Loading tickets with params:", params);
  //   // 实际加载票务数据的逻辑
  // }
});
