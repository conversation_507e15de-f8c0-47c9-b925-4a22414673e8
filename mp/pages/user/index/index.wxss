@import '/app.wxss';

.header-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 500rpx;
  background: linear-gradient(135deg, #5c7cff 0%, #2e38a8 100%);
  z-index: 0;
}

.profile-section {
  position: absolute;
  display: flex;
  z-index: 10;
  margin-top: 200rpx;
  align-items: center;
  flex-direction: column;
}

.avatar-wrapper {
  display: flex;
  width: 210rpx;
  height: 210rpx;
  border-radius: 50%;
  overflow: hidden;
  background-color: #8297f1;
  justify-content: center;
  align-items: center;
  margin-bottom: 20rpx;
}

.avatar {
  width: 180rpx;
  height: 180rpx;
  padding-top: 50rpx;
}

.nickname {
  font-size: 37rpx;
  font-weight: 700;
  color: #2e38a8;
}

.content-wrapper {
  position: relative;
  width: 100%;
  margin-top: 350rpx;
  z-index: 5;
  border-radius: 50rpx 50rpx 0 0;
  background-color: #f2f2f2;
}

.menu-section {
  width: 95vw;
  margin: 180rpx auto 0 auto;
  border-radius: 25rpx;
  overflow: hidden;
}

.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx;
  margin: 30rpx 5rpx;
  background-color: #fff;
  border-radius: 25rpx;
  box-shadow: 0 0 5rpx rgba(0, 0, 0, 0.15);
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item text {
  font-size: 32rpx;
  color: #333;
  font-weight: 400;
}

.arrow-right {
  width: 20rpx;
  height: 20rpx;
  fill: #ccc;
}